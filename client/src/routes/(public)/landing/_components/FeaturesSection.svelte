<script>
	import { gsap } from "gsap";
	import { ScrollTrigger } from "gsap/ScrollTrigger";
	import { onMount } from "svelte";
	import FeatureCard from "./FeatureCard.svelte";

	onMount(() => {
		// Animations after component is mounted
		// Register ScrollTrigger
		gsap.registerPlugin(ScrollTrigger);

		let tl = gsap.timeline({
			scrollTrigger: {
				trigger: ".anim-container"
			}
		});

		tl.from(".anim-container", {
			opacity: 0,
			y: 50,
			duration: 0.5
		}).from(".anim", {
			opacity: 0,
			y: 20,
			stagger: 0.2
		});
	});
</script>

<div class="anim-container bg-white px-6 py-24 sm:px-8">
	<div class="mx-auto max-w-4xl xl:max-w-6xl">
		<h2 class="anim mx-auto mb-12 w-full text-center text-3xl font-bold sm:w-2/3">
			Quickly find the home you want using our effective search filters!
		</h2>

		<div class="grid grid-cols-1 gap-8 md:grid-cols-3 lg:gap-12 xl:gap-16">
			{#each Array(3) as _, index (index)}
				<div class="anim">
					<FeatureCard
						imageSrc={`/landing-search${3 - index}.png`}
						title={[
							"Trustworthy and Verified Listings",
							"Browse Rental Listings with Ease",
							"Simplify your Rental Search with Advanced"
						][index]}
						description={[
							"Discover the best rental options with user reviews and ratings.",
							"Get access to user reviews and ratings for a better understanding of rental options.",
							"Find trustworthy and verified rental listigs to ensure a hustle-free experience."
						][index]}
						linkText={["Explore", "Search", "Discover"][index]}
						linkHref={["/explore", "/search", "/discover"][index]}
					/>
				</div>
			{/each}
		</div>
	</div>
</div>
