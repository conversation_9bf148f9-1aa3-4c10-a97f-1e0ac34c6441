<script lang="ts">
	let { imageSrc, title, description }: { imageSrc: string; title: string; description: string } =
		$props();
</script>

<div class="bg-primary-50 rounded-lg px-4 py-12 text-center shadow-lg md:h-72">
	<div class="bg-primary-700 mx-auto mb-4 h-10 w-10 rounded-full p-[0.6rem]">
		<img src={imageSrc} alt={title} class="size-30 h-full w-full" />
	</div>
	<h3 class="mt-4 text-xl font-medium text-gray-800">{title}</h3>
	<p class="mt-2 text-base text-gray-500">{description}</p>
</div>
