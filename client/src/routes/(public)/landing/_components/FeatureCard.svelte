<script lang="ts">
	let {
		imageSrc,
		title,
		description,
		linkText,
		linkHref
	}: { imageSrc: string; title: string; description: string; linkText: string; linkHref: string } =
		$props();
</script>

<div class="text-center">
	<div class="mb-4 flex h-48 items-center justify-center rounded-lg p-4">
		<img src={imageSrc} alt={title} class="size-400 h-full w-full object-contain" />
	</div>
	<h3 class="mb-2 text-xl font-semibold">{title}</h3>
	<p class="mb-4">{description}</p>
	<a href={linkHref} class="inline-block rounded border border-gray-300 px-4 py-2 hover:bg-gray-100"
		>{linkText}</a
	>
</div>
