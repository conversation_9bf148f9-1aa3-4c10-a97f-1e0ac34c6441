<script lang="ts">
	import { gsap } from "gsap";
	import { ScrollTrigger } from "gsap/ScrollTrigger";
	import { onMount } from "svelte";

	onMount(() => {
		gsap.registerPlugin(ScrollTrigger);

		gsap.from(".search-anim", {
			scrollTrigger: ".search-anim",
			opacity: 0,
			y: 20,
			duration: 0.5
		});
	});
</script>

<div class="relative py-24">
	<img
		src="/landing-call-to-action.jpg"
		alt="Promann Search Section Background"
		class="absolute inset-0 h-full w-full object-cover object-center"
	/>

	<div class="absolute inset-0 bg-black/60"></div>

	<div
		class="search-anim relative mx-auto max-w-4xl px-6 py-12 sm:px-8 lg:px-12 xl:max-w-6xl xl:px-16"
	>
		<div class="flex flex-col items-center justify-between md:flex-row">
			<div class="mb-6 md:mr-10 md:mb-0">
				<h2 class="text-2xl font-bold text-white">Find Your Dream Rental Property</h2>
			</div>

			<div>
				<p class="mb-3 text-white">
					Discover a wide range of rental properties in your desired location
				</p>

				<div class="flex justify-center gap-4 lg:justify-start">
					<button
						onclick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
						class="text-primary-700 hover:bg-primary-500 hover:text-primary-50 inline-block cursor-pointer rounded-lg bg-white px-6 py-3 font-semibold"
						>Search</button
					>

					<a
						href="/signup"
						class="bg-secondary-500 hover:bg-secondary-600 inline-block rounded-lg px-6 py-3 font-semibold text-white"
						>Sign Up</a
					>
				</div>
			</div>
		</div>
	</div>
</div>
