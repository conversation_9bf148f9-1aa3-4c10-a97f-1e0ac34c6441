<script>
	import { gsap } from "gsap";
	import { ScrollTrigger } from "gsap/ScrollTrigger";
	import { onMount } from "svelte";
	import FeatureCard from "./FeatureCard.svelte";
	import DiscoverCard from "./DiscoverCard.svelte";

	const discoverItems = [
		{
			imageSrc: "/landing-icon-wand.png",
			title: "Search for Properties",
			description:
				"Browse through our extensive collection of rental properties in your desired location."
		},
		{
			imageSrc: "/landing-icon-calendar.png",
			title: "Book your Rental",
			description:
				"Once you have found the perfect rental property, book it online with just a few clicks."
		},
		{
			imageSrc: "/landing-icon-heart.png",
			title: "Enjoy your New Home",
			description: "Move into your new rental property and start enjoying your dream home."
		}
	];

	onMount(() => {
		// Animations after component is mounted
		// Register ScrollTrigger
		gsap.registerPlugin(ScrollTrigger);

		let tl = gsap.timeline({
			scrollTrigger: {
				trigger: ".discover-anim-container"
			}
		});

		tl.from(".discover-anim-container", {
			opacity: 0
		}).from(".discover-anim", {
			opacity: 0,
			y: 20,
			stagger: 0.2
		});
	});
</script>

<div class="mb-16 bg-white py-12">
	<div class="mx-auto max-w-6xl px-6 sm:px-8 lg:px-12 xl:max-w-7xl xl:px-16">
		<div class="my-12 text-center">
			<h2 class="text-3xl leading-tight font-semibold text-gray-800">Discover</h2>
			<p class="mt-4 text-lg text-gray-600">Find your Dream Rental Property Today!</p>
			<p class="mx-auto mt-2 max-w-3xl text-gray-500">
				Searching for your dream rental property has never been easier. With our user-friendly
				search feature, you can quickly find the perfect home that meets all your needs. Start your
				search today and discover your dream rental property!
			</p>
		</div>

		<div class="grid grid-cols-1 gap-8 md:grid-cols-3 lg:gap-12 xl:gap-16">
			{#each discoverItems as item (item.description)}
				<div class="discover-anim">
					<DiscoverCard {...item} />
				</div>
			{/each}
		</div>
	</div>
</div>
