<script lang="ts">
	import { Button } from "$lib/components/ui/button";
	import Input from "$lib/components/ui/input/input.svelte";
	import { gsap } from "gsap";

	function animation(element: HTMLDivElement) {
		gsap.fromTo(element, { opacity: 0, y: 20 }, { opacity: 1, y: 0, duration: 0.8 });
	}
</script>

<div class="relative h-screen">
	<img
		src="/landing-splash.jpg"
		alt="Promann Hero Section"
		class="absolute inset-0 h-full w-full object-cover object-center"
	/>
	<div class="absolute inset-0 bg-black opacity-60"></div>
	<div {@attach animation} class="-transalate-y-1/2 absolute top-1/3 w-full transform text-center">
		<div class="mx-auto max-w-4xl px-16 sm:px-12">
			<h1 class="mb-4 text-5xl font-bold text-white">
				Start Your Journey to Finding Your Perfect Space
			</h1>
			<p class="mb-8 text-xl text-white">
				Explore our wide range of rental properties tailored to fit your needs
			</p>
			<div class="flex justify-center">
				<Input
					type="text"
					value="search query"
					placeholder="Search by city, neighborhood, or address"
					class="h-12 w-full max-w-lg rounded-none rounded-l-xl border-none bg-white"
				/>
				<Button
					onclick={() => {}}
					class="bg-secondary-500 hover:bg-secondary-600 h-12 rounded-none rounded-r-xl border-none text-white"
				>
					Search
				</Button>
			</div>
		</div>
	</div>
</div>
