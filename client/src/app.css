@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme {
	/* Primary color scale - converted from original hex values */
	--color-primary-50: oklch(0.988 0.001 106); /* #fcfcfc */
	--color-primary-100: oklch(0.947 0.001 286); /* #f1f1f2 */
	--color-primary-200: oklch(0.883 0.002 286); /* #e0e0e2 */
	--color-primary-300: oklch(0.796 0.003 286); /* #c7c7cc */
	--color-primary-400: oklch(0.693 0.004 286); /* #a8a8af */
	--color-primary-500: oklch(0.565 0.006 286); /* #82828b */
	--color-primary-600: oklch(0.384 0.008 286); /* #57575f */
	--color-primary-700: oklch(0.192 0.004 286); /* #27272a */
	--color-primary-800: oklch(0.106 0.002 286); /* #111113 */
	--color-primary-900: oklch(0.043 0.001 286); /* #040405 */
	--color-primary-950: oklch(0 0 0); /* #000000 */

	/* Secondary color scale - converted from original hex values */
	--color-secondary-50: oklch(0.991 0.001 25); /* #fefcfc */
	--color-secondary-100: oklch(0.967 0.008 25); /* #fdf2f2 */
	--color-secondary-200: oklch(0.918 0.02 25); /* #fae1e1 */
	--color-secondary-300: oklch(0.844 0.04 25); /* #f6c9c9 */
	--color-secondary-400: oklch(0.762 0.06 25); /* #f1abab */
	--color-secondary-500: oklch(0.675 0.082 25); /* #eb8686 */
	--color-secondary-600: oklch(0.57 0.112 25); /* #e45a5a */
	--color-secondary-700: oklch(0.482 0.142 25); /* #dc2828 */
	--color-secondary-800: oklch(0.279 0.078 25); /* #7c1414 */
	--color-secondary-900: oklch(0.171 0.05 25); /* #400a0a */
	--color-secondary-950: oklch(0.137 0.041 25); /* #2c0707 */

	/* Chart colors */
	--color-chart-1: oklch(0.65 0.18 25);
	--color-chart-2: oklch(0.55 0.12 180);
	--color-chart-3: oklch(0.4 0.08 210);
	--color-chart-4: oklch(0.75 0.15 85);
	--color-chart-5: oklch(0.7 0.2 55);

	--radius: 0.5rem;

	/* Logo Colors */
	--color-logo-rect: oklch(0.35 0.02 264);
	--color-logo-circle: oklch(0.55 0.22 264);
}

:root {
	--background: 0 0% 98%;
	--foreground: 222.2 84% 4.9%;
	--card: 0 0% 100%;
	--card-foreground: 222.2 84% 4.9%;
	--popover: 0 0% 100%;
	--popover-foreground: 222.2 84% 4.9%;
	--primary: 221.2 83.2% 53.3%;
	--primary-foreground: 210 40% 98%;
	--secondary: 210 40% 96.1%;
	--secondary-foreground: 222.2 47.4% 11.2%;
	--muted: 210 40% 96.1%;
	--muted-foreground: 215.4 16.3% 46.9%;
	--accent: 210 40% 96.1%;
	--accent-foreground: 222.2 47.4% 11.2%;
	--destructive: 0 84.2% 60.2%;
	--destructive-foreground: 210 40% 98%;
	--border: 214.3 31.8% 91.4%;
	--input: 214.3 31.8% 88%;
	--ring: 221.2 83.2% 53.3%;
	--radius: 0.5rem;
	--sidebar-background: 0 0% 98%;
	--sidebar-foreground: 240 5.3% 26.1%;
	--sidebar-primary: 240 5.9% 10%;
	--sidebar-primary-foreground: 0 0% 98%;
	--sidebar-accent: 240 4.8% 95.9%;
	--sidebar-accent-foreground: 240 5.9% 10%;
	--sidebar-border: 220 13% 91%;
	--sidebar-ring: 217.2 91.2% 59.8%;
}

.dark {
	--background: 222.2 84% 3.9%;
	--foreground: 210 40% 98%;
	--card: 222.2 84% 4.9%;
	--card-foreground: 210 40% 98%;
	--popover: 222.2 84% 4.9%;
	--popover-foreground: 210 40% 98%;
	--primary: 217.2 91.2% 59.8%;
	--primary-foreground: 222.2 47.4% 11.2%;
	--secondary: 217.2 32.6% 17.5%;
	--secondary-foreground: 210 40% 98%;
	--muted: 217.2 32.6% 17.5%;
	--muted-foreground: 215 20.2% 65.1%;
	--accent: 217.2 32.6% 17.5%;
	--accent-foreground: 210 40% 98%;
	--destructive: 0 62.8% 30.6%;
	--destructive-foreground: 210 40% 98%;
	--border: 217.2 32.6% 17.5%;
	--input: 217.2 32.6% 15.5%;
	--ring: 224.3 76.3% 48%;
	--sidebar-background: 240 5.9% 10%;
	--sidebar-foreground: 240 4.8% 95.9%;
	--sidebar-primary: 224.3 76.3% 48%;
	--sidebar-primary-foreground: 0 0% 100%;
	--sidebar-accent: 240 3.7% 15.9%;
	--sidebar-accent-foreground: 240 4.8% 95.9%;
	--sidebar-border: 240 3.7% 15.9%;
	--sidebar-ring: 217.2 91.2% 59.8%;
}

@theme inline {
   --radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);
}

*,
*::before,
*::after {
	box-sizing: border-box;
}

html,
body,
#root,
.app {
	height: 100%;
	width: 100%;
	@apply text-sm;
	@apply bg-white;
	@apply dark:bg-black;
}

* {
	@apply border;
}

body {
	@apply bg-background text-foreground;
}

.dashboard-container {
	@apply px-8 pt-8 pb-5;
}

.mapboxgl-popup-content {
	@apply !bg-primary-700;
	@apply !rounded-lg;
	@apply !py-2;
	@apply !px-3;
}

.mapboxgl-popup-anchor-top .mapboxgl-popup-tip {
	@apply !border-b-primary-700;
}

.mapboxgl-popup-anchor-bottom .mapboxgl-popup-tip {
	@apply !border-t-primary-700;
}

.mapboxgl-popup-anchor-left .mapboxgl-popup-tip {
	@apply !border-r-primary-700;
}

.mapboxgl-popup-anchor-right .mapboxgl-popup-tip {
	@apply !border-l-primary-700;
}

.marker-popup {
	@apply bg-primary-700;
	@apply text-white;
	@apply p-0;
	@apply m-0;
	@apply !flex;
	@apply justify-between;
	@apply items-center;
	@apply gap-3;
}

.marker-popup-image {
	@apply h-10 w-10 rounded-lg bg-white object-cover;
}

.marker-popup-price {
	@apply text-primary-200 text-sm font-semibold;
}

.marker-popup-title {
	@apply cursor-pointer hover:text-blue-300 hover:underline;
	@apply focus:outline-none;
}

.marker-popup-price-unit {
	@apply text-primary-500 text-xs font-normal;
}

/* scrollbar styling */
::-webkit-scrollbar {
	width: 5px;
}

::-webkit-scrollbar-track {
	background: transparent;
}

::-webkit-scrollbar-thumb {
	@apply bg-primary-200;
	border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
	@apply bg-primary-300;
}

/* Amplify UI Overrides */
[data-amplify-authenticator] {
	--amplify-components-button-primary-background-color: hsl(var(--color-primary));
	--amplify-components-button-primary-hover-background-color: hsl(var(--color-primary) / 0.9);
	--amplify-components-button-border-radius: var(--radius);
	--amplify-components-fieldcontrol-border-radius: var(--radius);
}

[data-amplify-authenticator][data-variation="default"] {
	height: 100%;
	padding: 2rem !important;
}

[data-amplify-authenticator] [data-amplify-router] {
	border: none !important;
	box-shadow: none !important;
	max-width: 400px !important;
	margin: 0 auto;
}

[data-amplify-authenticator] [data-amplify-container] {
	border-radius: var(--radius);
	padding: 2rem !important;
	border: 1px solid hsl(var(--color-border));
}

[data-amplify-authenticator] [data-amplify-form] {
	padding: 0 !important;
}

[data-amplify-authenticator] .amplify-tabs__list {
	display: none;
}

[data-amplify-authenticator] .amplify-button--primary {
	width: 100%;
	height: 2.75rem;
	margin-top: 1rem;
	background-color: hsl(var(--color-primary)) !important;
	color: hsl(var(--color-primary-foreground)) !important;
	@apply font-medium;
	@apply text-sm;
}

[data-amplify-authenticator] .amplify-button--primary:hover {
	background-color: hsl(var(--color-primary) / 0.8) !important;
}

[data-amplify-authenticator] .amplify-field-group__control {
	border-color: hsl(var(--color-input));
}

[data-amplify-authenticator] .amplify-field-group__control:focus-within {
	border-color: hsl(var(--color-ring)) !important;
	box-shadow: 0 0 0 1px hsl(var(--color-ring)) !important;
}

[data-amplify-authenticator] .amplify-field__show-password {
	color: hsl(var(--color-muted-foreground));
}

[data-amplify-authenticator] .amplify-label {
	@apply text-sm font-medium;
	color: hsl(var(--color-foreground));
}

[data-amplify-authenticator] .amplify-select {
	border-color: hsl(var(--color-input));
	border-radius: var(--radius);
	height: 2.5rem;
	@apply text-sm;
}

[data-amplify-authenticator] .amplify-text--error {
	color: #ff0000;
}

/* Sonner Toast Styles */
[data-close-button="true"] {
	@apply bg-background border-border text-foreground hover:bg-muted;
}
