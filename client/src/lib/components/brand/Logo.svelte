<script lang="ts">
	import type { HTMLAttributes } from "svelte/elements";

	let { ...props }: HTMLAttributes<SVGElement> = $props();
</script>

<svg
	id="eYUslmTgbhi1"
	xmlns="http://www.w3.org/2000/svg"
	xmlns:xlink="http://www.w3.org/1999/xlink"
	viewBox="0 0 300 300"
	shape-rendering="geometricPrecision"
	text-rendering="geometricPrecision"
	{...props}
	><g transform="translate(-16.482254 0.000008)"
		><ellipse
			rx="79.781613"
			ry="86.650229"
			transform="matrix(1.154904 0 0 1.10976 203.628228 96.160958)"
			class="text-logo-circle"
			fill="currentColor"
			stroke-width="0"
		/><rect
			width="30"
			height="30"
			rx="5"
			ry="5"
			transform="matrix(4.952796 0 0 10 37.196184 0)"
			class="text-logo-rect"
			fill="currentColor"
			stroke-width="0"
		/></g
	></svg
>
