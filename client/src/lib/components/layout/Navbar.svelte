<script>
	import { NAVBAR_HEIGHT } from "$lib/constants";
	import Logo from "../brand/Logo.svelte";
	import Button from "../ui/button/button.svelte";
</script>

<div class={`fixed top-0 left-0 z-50 w-full shadow-xl h-[${NAVBAR_HEIGHT}px]`}>
	<div class="bg-primary-700 flex w-full items-center justify-between px-8 py-3">
		<div class="flex items-center gap-4 md:gap-6">
			<a href="/" class="scroll-false"
				><div class="flex items-center gap-3">
					<Logo class="size-6 h-6 w-6" />
					<div class="text-foreground text-xl font-bold">
						PRO<span class="text-secondary-500 hover:text-primary-300 font-light">MANN</span>
					</div>
				</div>
			</a>
		</div>
		<p class="text-primary-200 hidden md:block">
			Discover your perfect rental with our advanced search
		</p>
		<div class="flex items-center gap-5">
			<Button
				href="/signin"
				variant="outline"
				class="hover:text-primary-700 rounded-lg border-white bg-transparent text-white hover:bg-white"
				>Sign In</Button
			>
			<Button
				href="/signup"
				variant="outline"
				class="bg-secondary-600 hover:text-primary-700 rounded-lg hover:bg-white">Sign Up</Button
			>
		</div>
	</div>
</div>
