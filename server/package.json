{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "rimraf dist && npx tsc", "start": "pnpm run build && node dist/index.js", "dev": "pnpm run build && concurrently \"npx tsc -w\" \"nodemon --exec ts-node src/index.ts\"", "seed": "ts-node prisma/seed.ts", "prisma:generate": "prisma generate", "postprisma:generate": "shx cp -r node_modules/@prisma/client/index.d.ts ../client/src/types/prismaTypes.d.ts"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.10.0", "dependencies": {"@aws-sdk/client-s3": "^3.842.0", "@aws-sdk/lib-storage": "^3.842.0", "@prisma/client": "^6.11.0", "@terraformer/wkt": "^2.2.1", "axios": "^1.10.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "prisma": "^6.11.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/body-parser": "^1.19.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/helmet": "^4.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^2.0.0", "@types/node": "^24.0.10", "@types/terraformer__wkt": "^2.0.3", "@types/uuid": "^10.0.0", "concurrently": "^9.2.0", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "shx": "^0.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}