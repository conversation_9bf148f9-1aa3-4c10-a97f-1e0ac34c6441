[{"id": 1, "name": "Sunny Downtown Apartment", "description": "A beautiful apartment in the heart of downtown with plenty of natural light.", "pricePerMonth": 1500.0, "securityDeposit": 1500.0, "applicationFee": 50.0, "photoUrls": ["https://example.com/apartment1_1.jpg", "https://example.com/apartment1_2.jpg"], "amenities": ["AirConditioning", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Parking"], "highlights": ["HighSpeedInternetAccess", "CloseToTransit"], "isPetsAllowed": true, "isParkingIncluded": false, "beds": 2, "baths": 1, "squareFeet": 800, "propertyType": "Apartment", "postedDate": "2023-05-15T00:00:00Z", "averageRating": 4.5, "numberOfReviews": 10, "locationId": 1, "managerCognitoId": "010be580-60a1-70ae-780e-18a6fd94ad32"}, {"id": 2, "name": "Cozy Beach House", "description": "A charming beach house with stunning ocean views.", "pricePerMonth": 2000.0, "securityDeposit": 2000.0, "applicationFee": 75.0, "photoUrls": ["https://example.com/beachhouse1.jpg", "https://example.com/beachhouse2.jpg"], "amenities": ["AirConditioning", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Parking", "Pool"], "highlights": ["GreatView", "CloseToTransit"], "isPetsAllowed": true, "isParkingIncluded": true, "beds": 3, "baths": 2, "squareFeet": 1200, "propertyType": "Villa", "postedDate": "2023-06-01T00:00:00Z", "averageRating": 4.8, "numberOfReviews": 5, "locationId": 2, "managerCognitoId": "us-east-2:23456789-90ab-cdef-1234-567890abcdef"}, {"id": 3, "name": "Modern City Loft", "description": "Sleek and stylish loft in the heart of the city.", "pricePerMonth": 2200.0, "securityDeposit": 2200.0, "applicationFee": 60.0, "photoUrls": ["https://example.com/cityloft1.jpg", "https://example.com/cityloft2.jpg"], "amenities": ["AirConditioning", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gym"], "highlights": ["HighSpeedInternetAccess", "CloseToTransit"], "isPetsAllowed": true, "isParkingIncluded": false, "beds": 1, "baths": 1, "squareFeet": 900, "propertyType": "Apartment", "postedDate": "2023-07-01T00:00:00Z", "averageRating": 4.7, "numberOfReviews": 8, "locationId": 3, "managerCognitoId": "us-east-2:34567890-90ab-cdef-1234-567890abcdef"}, {"id": 4, "name": "Spacious Family Home", "description": "Large family home with a beautiful backyard and modern amenities.", "pricePerMonth": 2500.0, "securityDeposit": 2500.0, "applicationFee": 80.0, "photoUrls": ["https://example.com/familyhome1.jpg", "https://example.com/familyhome2.jpg"], "amenities": ["AirConditioning", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Parking", "Dishwasher"], "highlights": ["QuietNeighborhood"], "isPetsAllowed": true, "isParkingIncluded": false, "beds": 4, "baths": 3, "squareFeet": 2000, "propertyType": "Villa", "postedDate": "2023-06-15T00:00:00Z", "averageRating": 4.9, "numberOfReviews": 12, "locationId": 4, "managerCognitoId": "us-east-2:45678901-90ab-cdef-1234-567890abcdef"}, {"id": 5, "name": "Luxury Penthouse", "description": "Stunning penthouse with panoramic city views and high-end finishes.", "pricePerMonth": 5000.0, "securityDeposit": 5000.0, "applicationFee": 100.0, "photoUrls": ["https://example.com/penthouse1.jpg", "https://example.com/penthouse2.jpg"], "amenities": ["AirConditioning", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gym", "Pool"], "highlights": ["GreatView"], "isPetsAllowed": true, "isParkingIncluded": false, "beds": 3, "baths": 3, "squareFeet": 2500, "propertyType": "Apartment", "postedDate": "2023-07-01T00:00:00Z", "averageRating": 5.0, "numberOfReviews": 15, "locationId": 5, "managerCognitoId": "us-east-2:56789012-90ab-cdef-1234-567890abcdef"}, {"id": 6, "name": "Cozy Studio Apartment", "description": "Efficient studio apartment perfect for students or young professionals.", "pricePerMonth": 1200.0, "securityDeposit": 1200.0, "applicationFee": 40.0, "photoUrls": ["https://example.com/studio1.jpg", "https://example.com/studio2.jpg"], "amenities": ["AirConditioning", "HighSpeedInternet"], "highlights": ["CloseToTransit"], "isPetsAllowed": true, "isParkingIncluded": false, "beds": 0, "baths": 1, "squareFeet": 400, "propertyType": "Apartment", "postedDate": "2023-08-01T00:00:00Z", "averageRating": 4.2, "numberOfReviews": 6, "locationId": 6, "managerCognitoId": "us-east-2:67890123-90ab-cdef-1234-567890abcdef"}, {"id": 7, "name": "Historic Brownstone", "description": "Charming brownstone with original features and modern updates.", "pricePerMonth": 3000.0, "securityDeposit": 3000.0, "applicationFee": 70.0, "photoUrls": ["https://example.com/brownstone1.jpg", "https://example.com/brownstone2.jpg"], "amenities": ["AirConditioning", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HighSpeedInternet"], "highlights": ["RecentlyRenovated"], "isPetsAllowed": true, "isParkingIncluded": false, "beds": 3, "baths": 2, "squareFeet": 1800, "propertyType": "Townhouse", "postedDate": "2023-09-01T00:00:00Z", "averageRating": 4.6, "numberOfReviews": 9, "locationId": 7, "managerCognitoId": "us-east-2:78901234-90ab-cdef-1234-567890abcdef"}, {"id": 8, "name": "Urban Micro-Apartment", "description": "Compact and efficient living space in the heart of the city.", "pricePerMonth": 1000.0, "securityDeposit": 1000.0, "applicationFee": 30.0, "photoUrls": ["https://example.com/micro1.jpg", "https://example.com/micro2.jpg"], "amenities": ["AirConditioning", "HighSpeedInternet"], "highlights": ["CloseToTransit"], "isPetsAllowed": true, "isParkingIncluded": false, "beds": 0, "baths": 1, "squareFeet": 300, "propertyType": "Apartment", "postedDate": "2023-08-01T00:00:00Z", "averageRating": 4.3, "numberOfReviews": 7, "locationId": 8, "managerCognitoId": "us-east-2:89012345-90ab-cdef-1234-567890abcdef"}, {"id": 9, "name": "Mountain View Cabin", "description": "Rustic cabin with breathtaking mountain views and modern amenities.", "pricePerMonth": 1800.0, "securityDeposit": 1800.0, "applicationFee": 60.0, "photoUrls": ["https://example.com/cabin1.jpg", "https://example.com/cabin2.jpg"], "amenities": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "AirConditioning"], "highlights": ["GreatView", "QuietNeighborhood"], "isPetsAllowed": true, "isParkingIncluded": false, "beds": 2, "baths": 1, "squareFeet": 1000, "propertyType": "Cottage", "postedDate": "2023-08-15T00:00:00Z", "averageRating": 4.9, "numberOfReviews": 11, "locationId": 9, "managerCognitoId": "us-east-2:90123456-90ab-cdef-1234-567890abcdef"}, {"id": 10, "name": "Eco-Friendly Tiny House", "description": "Sustainable living in a compact, well-designed tiny house.", "pricePerMonth": 900.0, "securityDeposit": 900.0, "applicationFee": 35.0, "photoUrls": ["https://example.com/tinyhouse1.jpg", "https://example.com/tinyhouse2.jpg"], "amenities": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "AirConditioning", "HighSpeedInternet"], "highlights": ["SmokeFree"], "isPetsAllowed": true, "isParkingIncluded": false, "beds": 1, "baths": 1, "squareFeet": 250, "propertyType": "Tinyhouse", "postedDate": "2023-08-10T00:00:00Z", "averageRating": 4.7, "numberOfReviews": 8, "locationId": 10, "managerCognitoId": "us-east-2:01234567-90ab-cdef-1234-567890abcdef"}]