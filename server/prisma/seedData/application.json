[{"id": 1, "applicationDate": "2023-05-20T00:00:00Z", "status": "Approved", "propertyId": 1, "tenantCognitoId": "us-east-2:98765432-90ab-cdef-1234-567890abcdef", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "I am interested in this property.", "leaseId": 1}, {"id": 2, "applicationDate": "2023-05-25T00:00:00Z", "status": "Pending", "propertyId": 2, "tenantCognitoId": "us-east-2:87654321-90ab-cdef-1234-567890abcdef", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "Looking forward to viewing this apartment."}, {"id": 3, "applicationDate": "2023-06-01T00:00:00Z", "status": "Denied", "propertyId": 3, "tenantCognitoId": "817b3540-a061-707b-742a-a28391181149", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "Excited about the possibility of renting this house."}, {"id": 4, "applicationDate": "2023-06-10T00:00:00Z", "status": "Approved", "propertyId": 4, "tenantCognitoId": "us-east-2:65432109-90ab-cdef-1234-567890abcdef", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "This property looks perfect for my needs.", "leaseId": 4}, {"id": 5, "applicationDate": "2023-06-15T00:00:00Z", "status": "Pending", "propertyId": 5, "tenantCognitoId": "us-east-2:54321098-90ab-cdef-1234-567890abcdef", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "I'm very interested in this apartment."}, {"id": 6, "applicationDate": "2023-06-20T00:00:00Z", "status": "Approved", "propertyId": 6, "tenantCognitoId": "us-east-2:43210987-90ab-cdef-1234-567890abcdef", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "This property seems to fit all my requirements.", "leaseId": 6}, {"id": 7, "applicationDate": "2023-06-25T00:00:00Z", "status": "Pending", "propertyId": 3, "tenantCognitoId": "us-east-2:32109876-90ab-cdef-1234-567890abcdef", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "I'm interested in this loft and would like to schedule a viewing."}, {"id": 8, "applicationDate": "2023-07-01T00:00:00Z", "status": "Approved", "propertyId": 5, "tenantCognitoId": "us-east-2:21098765-90ab-cdef-1234-567890abcdef", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "I'm very interested in this luxury penthouse.", "leaseId": 8}, {"id": 9, "applicationDate": "2023-07-05T00:00:00Z", "status": "Pending", "propertyId": 7, "tenantCognitoId": "us-east-2:10987654-90ab-cdef-1234-567890abcdef", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "The historic brownstone looks perfect for my family."}, {"id": 10, "applicationDate": "2023-07-10T00:00:00Z", "status": "Denied", "propertyId": 2, "tenantCognitoId": "us-east-2:09876543-90ab-cdef-1234-567890abcdef", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "I'd love to rent this beach house for the summer."}, {"id": 11, "applicationDate": "2023-07-15T00:00:00Z", "status": "Pending", "propertyId": 8, "tenantCognitoId": "us-east-2:a9876543-90ab-cdef-1234-567890abcdef", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "I'm interested in this micro-apartment for its central location."}, {"id": 12, "applicationDate": "2023-07-20T00:00:00Z", "status": "Approved", "propertyId": 9, "tenantCognitoId": "us-east-2:b9876543-90ab-cdef-1234-567890abcdef", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "The mountain view cabin looks perfect for a weekend getaway.", "leaseId": 12}, {"id": 13, "applicationDate": "2023-07-25T00:00:00Z", "status": "Pending", "propertyId": 10, "tenantCognitoId": "us-east-2:c9876543-90ab-cdef-1234-567890abcdef", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "I'm curious about the eco-friendly features of this tiny house."}, {"id": 14, "applicationDate": "2023-07-30T00:00:00Z", "status": "Approved", "propertyId": 1, "tenantCognitoId": "us-east-2:d9876543-90ab-cdef-1234-567890abcdef", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "The downtown apartment seems ideal for my work location.", "leaseId": 14}, {"id": 15, "applicationDate": "2023-08-05T00:00:00Z", "status": "Pending", "propertyId": 4, "tenantCognitoId": "us-east-2:e9876543-90ab-cdef-1234-567890abcdef", "name": "<PERSON>", "email": "<EMAIL>", "phoneNumber": "+****************", "message": "I'm looking for a spacious family home like this one."}]