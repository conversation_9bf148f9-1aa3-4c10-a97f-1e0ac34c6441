// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
   provider        = "prisma-client-js"
   previewFeatures = ["postgresqlExtensions"]
}

datasource db {
   provider   = "postgresql"
   url        = env("DATABASE_URL")
   extensions = [postgis]
}

enum Highlight {
   HighSpeedInternetAccess
   WasherDryer
   AirConditioning
   Heating
   SmokeFree
   CableReady
   SatelliteTV
   DoubleVanities
   TubShower
   Intercom
   SprinklerSystem
   RecentlyRenovated
   CloseToTransit
   GreatView
   QuietNeighborhood
}

enum Amenity {
   WasherDryer
   AirConditioning
   Dishwasher
   HighSpeedInternet
   HardwoodFloors
   WalkInClosets
   Microwave
   Refrigerator
   Pool
   Gym
   Parking
   PetsAllowed
   WiFi
}

enum PropertyType {
   Rooms
   TinyHouse
   Apartment
   Villa
   Townhouse
   Cottage
   Office
   Warehouse
}

enum ApplicationStatus {
   Pending
   Denied
   Approved
}

enum PaymentStatus {
   Pending
   Paid
   PartiallyPaid
   Overdue
}

model Property {
   id                Int           @id @default(autoincrement())
   name              String
   description       String
   pricePerMonth     Float
   securityDeposit   Float
   applicationFee    Float
   photoUrls         String[]
   amenities         Amenity[]
   highlights        Highlight[]
   isPetsAllowed     Boolean       @default(false)
   isParkingIncluded Boolean       @default(false)
   beds              Int
   baths             Float
   squareFeet        Int
   propertyType      PropertyType
   postedDate        DateTime      @default(now())
   avarageRating     Float?        @default(0)
   numberOfReviews   Int?          @default(0)
   locationId        Int
   managerCognitoId  String
   location          Location      @relation(fields: [locationId], references: [id])
   manager           Manager       @relation(fields: [managerCognitoId], references: [cognitoId])
   leases            Lease[]
   applications      Application[]
   favoritedBy       Tenant[]      @relation("TenantFavorites")
   tenants           Tenant[]      @relation("TenantProperties")
}

model Manager {
   id          Int    @id @default(autoincrement())
   cognitoId   String @unique
   name        String
   email       String
   phoneNumber String

   managedProperties Property[]
}

model Tenant {
   id          Int    @id @default(autoincrement())
   cognitoId   String @unique
   name        String
   email       String
   phoneNumber String

   propeties    Property[]    @relation("TenantProperties")
   favorites    Property[]    @relation("TenantFavorites")
   applications Application[]
   leases       Lease[]
}

model Location {
   id          Int                                    @id @default(autoincrement())
   address     String
   city        String
   state       String
   country     String
   postalCode  String
   coordinates Unsupported("geography(Point,  4326)")

   properties Property[]
}

model Application {
   id              Int               @id @default(autoincrement())
   applicationDate DateTime
   status          ApplicationStatus
   propertyId      Int
   tenantCognitoId String
   name            String
   email           String
   phoneNumber     String
   message         String
   leaseId         Int?              @unique

   property Property @relation(fields: [propertyId], references: [id])
   tenant   Tenant   @relation(fields: [tenantCognitoId], references: [cognitoId])
   lease    Lease?   @relation(fields: [leaseId], references: [id])
}

model Lease {
   id              Int      @id @default(autoincrement())
   startDate       DateTime
   endDate         DateTime
   rent            Float
   deposit         Float
   propertyId      Int
   tenantCognitoId String

   property    Property     @relation(fields: [propertyId], references: [id])
   tenant      Tenant       @relation(fields: [tenantCognitoId], references: [cognitoId])
   application Application?
   payments    Payment[]
}

model Payment {
   id            Int           @id @default(autoincrement())
   amountDue     Float
   amountPaid    Float
   dueDate       DateTime
   paymentDate   DateTime
   paymentStatus PaymentStatus
   leaseId       Int

   lease Lease @relation(fields: [leaseId], references: [id])
}
